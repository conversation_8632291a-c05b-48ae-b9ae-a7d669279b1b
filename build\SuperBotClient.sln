﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}"
	ProjectSection(ProjectDependencies) = postProject
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC} = {AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}
		{73BFA821-B7A3-3381-9999-345FC262FF11} = {73BFA821-B7A3-3381-9999-345FC262FF11}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{D4C81CB1-A64E-33BD-8BA0-723254656409}"
	ProjectSection(ProjectDependencies) = postProject
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6} = {D9D381E7-00A0-3E43-86E8-934B2A7D20F6}
		{73BFA821-B7A3-3381-9999-345FC262FF11} = {73BFA821-B7A3-3381-9999-345FC262FF11}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{23ABBD7E-9B91-31A3-BAB6-B063C2DC9187}"
	ProjectSection(ProjectDependencies) = postProject
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6} = {D9D381E7-00A0-3E43-86E8-934B2A7D20F6}
		{73BFA821-B7A3-3381-9999-345FC262FF11} = {73BFA821-B7A3-3381-9999-345FC262FF11}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SuperBotClient", "SuperBotClient.vcxproj", "{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}"
	ProjectSection(ProjectDependencies) = postProject
		{73BFA821-B7A3-3381-9999-345FC262FF11} = {73BFA821-B7A3-3381-9999-345FC262FF11}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{73BFA821-B7A3-3381-9999-345FC262FF11}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.Debug|x64.ActiveCfg = Debug|x64
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.Debug|x64.Build.0 = Debug|x64
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.Release|x64.ActiveCfg = Release|x64
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.Release|x64.Build.0 = Release|x64
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D9D381E7-00A0-3E43-86E8-934B2A7D20F6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D4C81CB1-A64E-33BD-8BA0-723254656409}.Debug|x64.ActiveCfg = Debug|x64
		{D4C81CB1-A64E-33BD-8BA0-723254656409}.Release|x64.ActiveCfg = Release|x64
		{D4C81CB1-A64E-33BD-8BA0-723254656409}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D4C81CB1-A64E-33BD-8BA0-723254656409}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{23ABBD7E-9B91-31A3-BAB6-B063C2DC9187}.Debug|x64.ActiveCfg = Debug|x64
		{23ABBD7E-9B91-31A3-BAB6-B063C2DC9187}.Release|x64.ActiveCfg = Release|x64
		{23ABBD7E-9B91-31A3-BAB6-B063C2DC9187}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{23ABBD7E-9B91-31A3-BAB6-B063C2DC9187}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.Debug|x64.ActiveCfg = Debug|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.Debug|x64.Build.0 = Debug|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.Release|x64.ActiveCfg = Release|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.Release|x64.Build.0 = Release|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{AE841391-2B11-37DC-BE9A-9E96EDFDBEEC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.Debug|x64.ActiveCfg = Debug|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.Debug|x64.Build.0 = Debug|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.Release|x64.ActiveCfg = Release|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.Release|x64.Build.0 = Release|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{73BFA821-B7A3-3381-9999-345FC262FF11}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {480845CF-8537-364B-A14F-F69C417F749A}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
