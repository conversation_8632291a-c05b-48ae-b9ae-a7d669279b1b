#include "include/Common.h"
#include <iostream>
#include <cstddef>

int main() {
    std::cout << "=== STRUCT SIZE ANALYSIS ===" << std::endl;
    std::cout << "MessageHeader size: " << sizeof(MessageHeader) << " bytes" << std::endl;

    std::cout << "\nField sizes:" << std::endl;
    std::cout << "  uint32_t magic: " << sizeof(uint32_t) << " bytes" << std::endl;
    std::cout << "  uint32_t length: " << sizeof(uint32_t) << " bytes" << std::endl;
    std::cout << "  uint16_t type: " << sizeof(uint16_t) << " bytes" << std::endl;
    std::cout << "  uint32_t sequence: " << sizeof(uint32_t) << " bytes" << std::endl;
    std::cout << "  uint16_t flags: " << sizeof(uint16_t) << " bytes" << std::endl;
    std::cout << "  uint32_t checksum: " << sizeof(uint32_t) << " bytes" << std::endl;
    std::cout << "  uint16_t version: " << sizeof(uint16_t) << " bytes" << std::endl;
    std::cout << "  uint16_t reserved: " << sizeof(uint16_t) << " bytes" << std::endl;

    uint32_t totalExpected = sizeof(uint32_t) * 4 + sizeof(uint16_t) * 4;
    std::cout << "\nExpected total: " << totalExpected << " bytes" << std::endl;
    std::cout << "Actual size: " << sizeof(MessageHeader) << " bytes" << std::endl;
    std::cout << "Padding: " << (sizeof(MessageHeader) - totalExpected) << " bytes" << std::endl;

    std::cout << "\n=== FIELD OFFSETS ===" << std::endl;
    MessageHeader header = {};
    std::cout << "magic offset: " << offsetof(MessageHeader, magic) << std::endl;
    std::cout << "length offset: " << offsetof(MessageHeader, length) << std::endl;
    std::cout << "type offset: " << offsetof(MessageHeader, type) << std::endl;
    std::cout << "sequence offset: " << offsetof(MessageHeader, sequence) << std::endl;
    std::cout << "flags offset: " << offsetof(MessageHeader, flags) << std::endl;
    std::cout << "checksum offset: " << offsetof(MessageHeader, checksum) << std::endl;
    std::cout << "version offset: " << offsetof(MessageHeader, version) << std::endl;
    std::cout << "reserved offset: " << offsetof(MessageHeader, reserved) << std::endl;

    return 0;
}
