﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}"
	ProjectSection(ProjectDependencies) = postProject
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A} = {7334C4B0-99D1-3ABC-9349-299F8F1C335A}
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29} = {EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{4D19BBBF-1965-3659-A946-7445C3779F07}"
	ProjectSection(ProjectDependencies) = postProject
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E} = {87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29} = {EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{0935A77F-5FB8-3BCE-B9AE-7CFCDC4117EF}"
	ProjectSection(ProjectDependencies) = postProject
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E} = {87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29} = {EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SuperBotClient", "SuperBotClient.vcxproj", "{7334C4B0-99D1-3ABC-9349-299F8F1C335A}"
	ProjectSection(ProjectDependencies) = postProject
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29} = {EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.Debug|x64.ActiveCfg = Debug|x64
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.Debug|x64.Build.0 = Debug|x64
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.Release|x64.ActiveCfg = Release|x64
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.Release|x64.Build.0 = Release|x64
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{87745AD1-CBB6-3E5D-BA3F-D573CBF4144E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4D19BBBF-1965-3659-A946-7445C3779F07}.Debug|x64.ActiveCfg = Debug|x64
		{4D19BBBF-1965-3659-A946-7445C3779F07}.Release|x64.ActiveCfg = Release|x64
		{4D19BBBF-1965-3659-A946-7445C3779F07}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4D19BBBF-1965-3659-A946-7445C3779F07}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0935A77F-5FB8-3BCE-B9AE-7CFCDC4117EF}.Debug|x64.ActiveCfg = Debug|x64
		{0935A77F-5FB8-3BCE-B9AE-7CFCDC4117EF}.Release|x64.ActiveCfg = Release|x64
		{0935A77F-5FB8-3BCE-B9AE-7CFCDC4117EF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0935A77F-5FB8-3BCE-B9AE-7CFCDC4117EF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.Debug|x64.ActiveCfg = Debug|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.Debug|x64.Build.0 = Debug|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.Release|x64.ActiveCfg = Release|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.Release|x64.Build.0 = Release|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7334C4B0-99D1-3ABC-9349-299F8F1C335A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.Debug|x64.ActiveCfg = Debug|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.Debug|x64.Build.0 = Debug|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.Release|x64.ActiveCfg = Release|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.Release|x64.Build.0 = Release|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EFB65B0C-D2A1-3C7A-906F-5A09EB0DDB29}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {405FA828-B794-32A0-92A3-9DAEE7B0D50D}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
