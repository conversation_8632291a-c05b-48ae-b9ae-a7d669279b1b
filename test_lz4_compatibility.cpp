#include <iostream>
#include <vector>
#include <fstream>
#include <lz4.h>

// Test LZ4 compression and save to file for server testing
int main() {
    std::cout << "=== LZ4 Client-Server Compatibility Test ===" << std::endl;
    
    // Create test data similar to screen frame
    std::vector<uint8_t> testData;
    
    // Generate 800x600x3 BGR24 test data
    int width = 800, height = 600;
    size_t dataSize = width * height * 3;
    testData.resize(dataSize);
    
    // Fill with pattern similar to screen data
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            size_t idx = (y * width + x) * 3;
            
            if (y < height / 4) {
                // Top area - blue gradient
                testData[idx] = 255 - (x * 255 / width);     // B
                testData[idx + 1] = 128;                     // G
                testData[idx + 2] = 64;                      // R
            } else if (y < height / 2) {
                // Middle area - green
                testData[idx] = 64;                          // B
                testData[idx + 1] = 200 - (x * 100 / width); // G
                testData[idx + 2] = 64;                      // R
            } else {
                // Bottom area - pattern
                testData[idx] = (x + y) % 256;               // B
                testData[idx + 1] = (x * 2) % 256;           // G
                testData[idx + 2] = (y * 2) % 256;           // R
            }
        }
    }
    
    std::cout << "Generated test data: " << testData.size() << " bytes" << std::endl;
    
    // Save original data
    std::ofstream originalFile("test_data_original.bin", std::ios::binary);
    originalFile.write(reinterpret_cast<const char*>(testData.data()), testData.size());
    originalFile.close();
    std::cout << "Saved original data to test_data_original.bin" << std::endl;
    
    // Compress with LZ4 (client method)
    int maxCompressedSize = LZ4_compressBound(testData.size());
    std::vector<uint8_t> compressed(maxCompressedSize);
    
    int compressedSize = LZ4_compress_default(
        reinterpret_cast<const char*>(testData.data()),
        reinterpret_cast<char*>(compressed.data()),
        testData.size(),
        maxCompressedSize
    );
    
    if (compressedSize <= 0) {
        std::cout << "❌ LZ4 compression failed!" << std::endl;
        return 1;
    }
    
    compressed.resize(compressedSize);
    std::cout << "✅ LZ4 compression successful:" << std::endl;
    std::cout << "  Original size: " << testData.size() << " bytes" << std::endl;
    std::cout << "  Compressed size: " << compressedSize << " bytes" << std::endl;
    std::cout << "  Compression ratio: " << (double)compressedSize / testData.size() * 100 << "%" << std::endl;
    
    // Save compressed data
    std::ofstream compressedFile("test_data_compressed.bin", std::ios::binary);
    compressedFile.write(reinterpret_cast<const char*>(compressed.data()), compressed.size());
    compressedFile.close();
    std::cout << "Saved compressed data to test_data_compressed.bin" << std::endl;
    
    // Test decompression (client method)
    std::vector<uint8_t> decompressed(testData.size());
    int decompressedSize = LZ4_decompress_safe(
        reinterpret_cast<const char*>(compressed.data()),
        reinterpret_cast<char*>(decompressed.data()),
        compressed.size(),
        testData.size()
    );
    
    if (decompressedSize != static_cast<int>(testData.size())) {
        std::cout << "❌ LZ4 decompression failed! Expected " << testData.size() 
                  << " bytes, got " << decompressedSize << " bytes" << std::endl;
        return 1;
    }
    
    // Verify data integrity
    bool dataMatch = true;
    for (size_t i = 0; i < testData.size(); i++) {
        if (testData[i] != decompressed[i]) {
            std::cout << "❌ Data mismatch at byte " << i << ": expected " 
                      << (int)testData[i] << ", got " << (int)decompressed[i] << std::endl;
            dataMatch = false;
            break;
        }
    }
    
    if (dataMatch) {
        std::cout << "✅ Client-side LZ4 round-trip successful!" << std::endl;
    } else {
        std::cout << "❌ Client-side LZ4 round-trip failed!" << std::endl;
        return 1;
    }
    
    // Save decompressed data for verification
    std::ofstream decompressedFile("test_data_decompressed.bin", std::ios::binary);
    decompressedFile.write(reinterpret_cast<const char*>(decompressed.data()), decompressed.size());
    decompressedFile.close();
    std::cout << "Saved decompressed data to test_data_decompressed.bin" << std::endl;
    
    // Create metadata file for server test
    std::ofstream metadataFile("test_metadata.txt");
    metadataFile << "original_size=" << testData.size() << std::endl;
    metadataFile << "compressed_size=" << compressed.size() << std::endl;
    metadataFile << "width=" << width << std::endl;
    metadataFile << "height=" << height << std::endl;
    metadataFile << "format=BGR24" << std::endl;
    metadataFile.close();
    std::cout << "Saved metadata to test_metadata.txt" << std::endl;
    
    std::cout << "\n=== Instructions for Server Test ===" << std::endl;
    std::cout << "1. Copy test_data_compressed.bin and test_metadata.txt to server" << std::endl;
    std::cout << "2. Run server test to decompress with K4os.Compression.LZ4" << std::endl;
    std::cout << "3. Compare results with test_data_original.bin" << std::endl;
    
    std::cout << "\n✅ Client test completed successfully!" << std::endl;
    return 0;
}
