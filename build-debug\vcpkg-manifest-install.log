Detecting compiler hash for triplet x64-windows...
Compiler found: C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.39.33519/bin/Hostx64/x64/cl.exe
The following packages are already installed:
    lz4:x64-windows@1.10.0
  * vcpkg-cmake:x64-windows@2024-04-23
  * vcpkg-cmake-config:x64-windows@2024-05-23
lz4 provides CMake targets:

  find_package(lz4 CONFIG REQUIRED)
  target_link_libraries(main PRIVATE lz4::lz4)

lz4 provides pkg-config modules:

  liblz4

All requested installations completed successfully in: 580 us
