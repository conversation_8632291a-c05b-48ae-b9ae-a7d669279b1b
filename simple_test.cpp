#include <iostream>
#include <vector>
#include <cstring>

// Simple test without external dependencies
int main() {
    std::cout << "=== Simple LZ4 Compression Test ===" << std::endl;
    
    // Test basic data structures
    std::cout << "Testing basic data structures..." << std::endl;
    
    // Test struct sizes
    struct TestRect {
        int x, y, width, height;
    };
    
    struct TestScreenFrame {
        int monitorId;
        TestRect region;
        int format;
        bool isFullFrame;
        uint64_t timestamp;
        bool isCompressed;
        uint32_t originalSize;
        uint32_t dataSize;
    };
    
    std::cout << "sizeof(int): " << sizeof(int) << " bytes" << std::endl;
    std::cout << "sizeof(TestRect): " << sizeof(TestRect) << " bytes" << std::endl;
    std::cout << "sizeof(bool): " << sizeof(bool) << " bytes" << std::endl;
    std::cout << "sizeof(uint64_t): " << sizeof(uint64_t) << " bytes" << std::endl;
    std::cout << "sizeof(uint32_t): " << sizeof(uint32_t) << " bytes" << std::endl;
    std::cout << "sizeof(TestScreenFrame): " << sizeof(TestScreenFrame) << " bytes" << std::endl;
    
    // Calculate expected serialized header size
    size_t expectedHeaderSize = sizeof(int) +          // monitorId
                                sizeof(TestRect) +     // region
                                sizeof(int) +          // format
                                sizeof(bool) +         // isFullFrame
                                sizeof(uint64_t) +     // timestamp
                                sizeof(bool) +         // isCompressed
                                sizeof(uint32_t) +     // originalSize
                                sizeof(uint32_t);      // dataSize
    
    std::cout << "Expected serialized header size: " << expectedHeaderSize << " bytes" << std::endl;
    
    // Test serialization simulation
    std::cout << "\nTesting serialization simulation..." << std::endl;
    
    TestScreenFrame frame;
    frame.monitorId = 1;
    frame.region = {0, 0, 800, 600};
    frame.format = 1; // BGR24
    frame.isFullFrame = true;
    frame.timestamp = 1234567890;
    frame.isCompressed = true;
    frame.originalSize = 1440000; // 800*600*3
    frame.dataSize = 720000;      // Compressed size
    
    // Simulate serialization
    std::vector<uint8_t> serializedData;
    serializedData.reserve(expectedHeaderSize);
    
    // Serialize each field
    const uint8_t* ptr;
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.monitorId);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.monitorId));
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.region);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.region));
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.format);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.format));
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.isFullFrame);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.isFullFrame));
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.timestamp);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.timestamp));
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.isCompressed);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.isCompressed));
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.originalSize);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.originalSize));
    
    ptr = reinterpret_cast<const uint8_t*>(&frame.dataSize);
    serializedData.insert(serializedData.end(), ptr, ptr + sizeof(frame.dataSize));
    
    std::cout << "Serialized header size: " << serializedData.size() << " bytes" << std::endl;
    
    // Test deserialization
    std::cout << "\nTesting deserialization simulation..." << std::endl;
    
    size_t offset = 0;
    TestScreenFrame deserializedFrame;
    
    std::memcpy(&deserializedFrame.monitorId, serializedData.data() + offset, sizeof(deserializedFrame.monitorId));
    offset += sizeof(deserializedFrame.monitorId);
    
    std::memcpy(&deserializedFrame.region, serializedData.data() + offset, sizeof(deserializedFrame.region));
    offset += sizeof(deserializedFrame.region);
    
    std::memcpy(&deserializedFrame.format, serializedData.data() + offset, sizeof(deserializedFrame.format));
    offset += sizeof(deserializedFrame.format);
    
    std::memcpy(&deserializedFrame.isFullFrame, serializedData.data() + offset, sizeof(deserializedFrame.isFullFrame));
    offset += sizeof(deserializedFrame.isFullFrame);
    
    std::memcpy(&deserializedFrame.timestamp, serializedData.data() + offset, sizeof(deserializedFrame.timestamp));
    offset += sizeof(deserializedFrame.timestamp);
    
    std::memcpy(&deserializedFrame.isCompressed, serializedData.data() + offset, sizeof(deserializedFrame.isCompressed));
    offset += sizeof(deserializedFrame.isCompressed);
    
    std::memcpy(&deserializedFrame.originalSize, serializedData.data() + offset, sizeof(deserializedFrame.originalSize));
    offset += sizeof(deserializedFrame.originalSize);
    
    std::memcpy(&deserializedFrame.dataSize, serializedData.data() + offset, sizeof(deserializedFrame.dataSize));
    offset += sizeof(deserializedFrame.dataSize);
    
    std::cout << "Deserialized header size: " << offset << " bytes" << std::endl;
    
    // Verify data
    bool success = true;
    if (deserializedFrame.monitorId != frame.monitorId) {
        std::cout << "❌ MonitorId mismatch: " << deserializedFrame.monitorId << " != " << frame.monitorId << std::endl;
        success = false;
    }
    
    if (deserializedFrame.region.width != frame.region.width || deserializedFrame.region.height != frame.region.height) {
        std::cout << "❌ Region mismatch" << std::endl;
        success = false;
    }
    
    if (deserializedFrame.format != frame.format) {
        std::cout << "❌ Format mismatch: " << deserializedFrame.format << " != " << frame.format << std::endl;
        success = false;
    }
    
    if (deserializedFrame.isFullFrame != frame.isFullFrame) {
        std::cout << "❌ IsFullFrame mismatch" << std::endl;
        success = false;
    }
    
    if (deserializedFrame.timestamp != frame.timestamp) {
        std::cout << "❌ Timestamp mismatch: " << deserializedFrame.timestamp << " != " << frame.timestamp << std::endl;
        success = false;
    }
    
    if (deserializedFrame.isCompressed != frame.isCompressed) {
        std::cout << "❌ IsCompressed mismatch" << std::endl;
        success = false;
    }
    
    if (deserializedFrame.originalSize != frame.originalSize) {
        std::cout << "❌ OriginalSize mismatch: " << deserializedFrame.originalSize << " != " << frame.originalSize << std::endl;
        success = false;
    }
    
    if (deserializedFrame.dataSize != frame.dataSize) {
        std::cout << "❌ DataSize mismatch: " << deserializedFrame.dataSize << " != " << frame.dataSize << std::endl;
        success = false;
    }
    
    if (success) {
        std::cout << "✅ All fields match!" << std::endl;
        std::cout << "  MonitorId: " << deserializedFrame.monitorId << std::endl;
        std::cout << "  Region: (" << deserializedFrame.region.x << ", " << deserializedFrame.region.y 
                  << ", " << deserializedFrame.region.width << ", " << deserializedFrame.region.height << ")" << std::endl;
        std::cout << "  Format: " << deserializedFrame.format << std::endl;
        std::cout << "  IsFullFrame: " << (deserializedFrame.isFullFrame ? "true" : "false") << std::endl;
        std::cout << "  Timestamp: " << deserializedFrame.timestamp << std::endl;
        std::cout << "  IsCompressed: " << (deserializedFrame.isCompressed ? "true" : "false") << std::endl;
        std::cout << "  OriginalSize: " << deserializedFrame.originalSize << std::endl;
        std::cout << "  DataSize: " << deserializedFrame.dataSize << std::endl;
    }
    
    std::cout << "\n=== Test Results ===" << std::endl;
    if (success) {
        std::cout << "✅ ALL TESTS PASSED!" << std::endl;
        std::cout << "Header size is " << serializedData.size() << " bytes (expected for server: 42 bytes)" << std::endl;
        return 0;
    } else {
        std::cout << "❌ SOME TESTS FAILED!" << std::endl;
        return 1;
    }
}
