{"version": "2.0.0", "tasks": [{"label": "Configure Debug Build", "type": "shell", "command": "cmake", "args": ["..", "-DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake", "-DCMAKE_BUILD_TYPE=Debug"], "options": {"cwd": "${workspaceFolder}/build-debug"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Build Debug", "type": "shell", "command": "cmake", "args": ["--build", ".", "--config", "Debug"], "options": {"cwd": "${workspaceFolder}/build-debug"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile", "$gcc"], "dependsOn": "Configure Debug Build"}, {"label": "Configure Release Build", "type": "shell", "command": "cmake", "args": ["..", "-DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake", "-DCMAKE_BUILD_TYPE=Release"], "options": {"cwd": "${workspaceFolder}/build"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Build Release", "type": "shell", "command": "cmake", "args": ["--build", ".", "--config", "Release"], "options": {"cwd": "${workspaceFolder}/build"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile", "$gcc"], "dependsOn": "Configure Release Build"}, {"label": "Clean Debug", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "clean"], "options": {"cwd": "${workspaceFolder}/build-debug"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Clean Release", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "clean"], "options": {"cwd": "${workspaceFolder}/build"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Install vcpkg dependencies", "type": "shell", "command": "./vcpkg/vcpkg", "args": ["install"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}