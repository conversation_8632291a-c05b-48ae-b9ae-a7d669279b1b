-- Configuring x64-windows
[1/1] "C:/Program Files/CMake/bin/cmake.exe" -E chdir ".." "C:/Program Files/CMake/bin/cmake.exe" "D:/Project/SuperBot/Client/vcpkg/scripts/detect_compiler" "-G" "Ninja" "-DCMAKE_BUILD_TYPE=Release" "-DCMAKE_INSTALL_PREFIX=D:/Project/SuperBot/Client/vcpkg/packages/detect_compiler_x64-windows" "-DVCPKG_COMPILER_CACHE_FILE=D:/Project/SuperBot/Client/build-debug/vcpkg_installed/vcpkg/compiler-file-hash-cache.json" "-DCMAKE_MAKE_PROGRAM=D:/Project/SuperBot/Client/vcpkg/downloads/tools/ninja/1.12.1-windows/ninja.exe" "-DBUILD_SHARED_LIBS=ON" "-DVCPKG_CHAINLOAD_TOOLCHAIN_FILE=D:/Project/SuperBot/Client/vcpkg/scripts/toolchains/windows.cmake" "-DVCPKG_TARGET_TRIPLET=x64-windows" "-DVCPKG_SET_CHARSET_FLAG=ON" "-DVCPKG_PLATFORM_TOOLSET=v143" "-DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON" "-DCMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY=ON" "-DCMAKE_FIND_PACKAGE_NO_SYSTEM_PACKAGE_REGISTRY=ON" "-DCMAKE_INSTALL_SYSTEM_RUNTIME_LIBS_SKIP=TRUE" "-DCMAKE_VERBOSE_MAKEFILE=ON" "-DVCPKG_APPLOCAL_DEPS=OFF" "-DCMAKE_TOOLCHAIN_FILE=D:/Project/SuperBot/Client/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DCMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION=ON" "-DVCPKG_CXX_FLAGS=" "-DVCPKG_CXX_FLAGS_RELEASE=" "-DVCPKG_CXX_FLAGS_DEBUG=" "-DVCPKG_C_FLAGS=" "-DVCPKG_C_FLAGS_RELEASE=" "-DVCPKG_C_FLAGS_DEBUG=" "-DVCPKG_CRT_LINKAGE=dynamic" "-DVCPKG_LINKER_FLAGS=" "-DVCPKG_LINKER_FLAGS_RELEASE=" "-DVCPKG_LINKER_FLAGS_DEBUG=" "-DVCPKG_TARGET_ARCHITECTURE=x64" "-DCMAKE_INSTALL_LIBDIR:STRING=lib" "-DCMAKE_INSTALL_BINDIR:STRING=bin" "-D_VCPKG_ROOT_DIR=D:/Project/SuperBot/Client/vcpkg" "-DZ_VCPKG_ROOT_DIR=D:/Project/SuperBot/Client/vcpkg" "-D_VCPKG_INSTALLED_DIR=D:/Project/SuperBot/Client/build-debug/vcpkg_installed" "-DVCPKG_MANIFEST_INSTALL=OFF"
-- The C compiler identification is MSVC 19.39.33523.0
-- The CXX compiler identification is MSVC 19.39.33523.0
#COMPILER_HASH#587828565ac2e9afff4fa4d7c3600946b389739d
#COMPILER_C_HASH#fba5b3a1daf73c4d2314d08ad3525ba26cde7d84
#COMPILER_C_VERSION#19.39.33523.0
#COMPILER_C_ID#MSVC
#COMPILER_C_PATH#C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.39.33519/bin/Hostx64/x64/cl.exe
#COMPILER_CXX_HASH#fba5b3a1daf73c4d2314d08ad3525ba26cde7d84
#COMPILER_CXX_VERSION#19.39.33523.0
#COMPILER_CXX_ID#MSVC
#COMPILER_CXX_PATH#C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.39.33519/bin/Hostx64/x64/cl.exe
-- Configuring done (1.1s)
-- Generating done (0.0s)
CMake Warning:
  Manually-specified variables were not used by the project:

    BUILD_SHARED_LIBS
    CMAKE_INSTALL_BINDIR
    CMAKE_INSTALL_LIBDIR
    _VCPKG_ROOT_DIR


-- Build files have been written to: D:/Project/SuperBot/Client/vcpkg/buildtrees/detect_compiler/x64-windows-rel

