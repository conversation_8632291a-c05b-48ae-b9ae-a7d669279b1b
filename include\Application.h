#pragma once

#include "Common.h"
#include <map>

class Application
{
public:
    explicit Application(int argc, char* argv[]);
    ~Application();

    // Singleton access
    static Application* instance();

    // Application lifecycle
    bool initialize();
    void shutdown();
    int run();

    // Configuration
    std::string getConfigValue(const std::string& key, const std::string& defaultValue = "") const;
    void setConfigValue(const std::string& key, const std::string& value);

    // Command line arguments
    bool isServiceMode() const { return m_serviceMode; }
    int getPort() const { return m_port; }
    std::string getLogLevel() const { return m_logLevel; }
    std::string getConfigFile() const { return m_configFile; }

    // Application state
    bool isRunning() const { return m_running; }
    void requestShutdown();

    // Event callbacks
    void onNetworkError(ErrorCode error, const std::string& message);
    void onClientConnected();
    void onClientDisconnected();
    void onQualityControlReceived(const QualityControlMessage& msg);

private:
    void parseCommandLine();
    void setupLogging();
    void setupSignalHandlers();
    void loadConfiguration();
    void saveConfiguration();
    bool startService();
    void stopService();

    // Core components
    std::unique_ptr<NetworkClient> m_networkClient;
    std::unique_ptr<SecurityManager> m_securityManager;
    std::unique_ptr<ScreenCapture> m_screenCapture;
    std::unique_ptr<InputHandler> m_inputHandler;
    std::unique_ptr<Logger> m_logger;

    // Configuration
    std::map<std::string, std::string> m_config;

    // Command line options
    bool m_serviceMode;
    int m_port;
    std::string m_logLevel;
    std::string m_configFile;
    std::string m_certificatePath;
    std::string m_keyPath;
    bool m_autoStart;
    bool m_allowRemoteInput;

    // Application state
    bool m_running;
    bool m_initialized;
    static Application* s_instance;

    // Command line arguments
    int m_argc;
    char** m_argv;

    // Windows service support
    void installService();
    void uninstallService();
    static void WINAPI serviceMain(DWORD argc, LPWSTR* argv);
    static void WINAPI serviceCtrlHandler(DWORD ctrl);
    static SERVICE_STATUS s_serviceStatus;
    static SERVICE_STATUS_HANDLE s_serviceStatusHandle;
};
