#include "Logger.h"
#include <iostream>
#include <iomanip>

Logger* Logger::s_instance = nullptr;

Logger* Logger::instance()
{
    if (!s_instance) {
        s_instance = new Logger();
    }
    return s_instance;
}

Logger::Logger()
    : m_logLevel(LogLevel::LOG_INFO)
    , m_consoleOutput(true)
    , m_eventLogOutput(true)
    , m_eventLogHandle(nullptr)
{
    // Initialize critical section
    InitializeCriticalSection(&m_criticalSection);

    // Register event source for Windows Event Log
    m_eventLogHandle = RegisterEventSourceW(nullptr, L"SuperBotClient");
}

Logger::~Logger()
{
    if (m_fileStream.is_open()) {
        m_fileStream.close();
    }

    if (m_eventLogHandle) {
        DeregisterEventSource(m_eventLogHandle);
    }

    // Cleanup critical section
    DeleteCriticalSection(&m_criticalSection);
}

void Logger::setLogLevel(LogLevel level)
{
    EnterCriticalSection(&m_criticalSection);
    m_logLevel = level;
    LeaveCriticalSection(&m_criticalSection);
}

void Logger::setLogFile(const std::string& filename)
{
    EnterCriticalSection(&m_criticalSection);

    if (m_fileStream.is_open()) {
        m_fileStream.close();
    }

    m_logFile = filename;
    m_fileStream.open(filename, std::ios::app);

    LeaveCriticalSection(&m_criticalSection);
}

void Logger::enableConsoleOutput(bool enable)
{
    EnterCriticalSection(&m_criticalSection);
    m_consoleOutput = enable;
    LeaveCriticalSection(&m_criticalSection);
}

void Logger::enableEventLog(bool enable)
{
    EnterCriticalSection(&m_criticalSection);
    m_eventLogOutput = enable;
    LeaveCriticalSection(&m_criticalSection);
}

void Logger::debug(const std::string& message)
{
    log(LogLevel::LOG_DEBUG, message);
}

void Logger::info(const std::string& message)
{
    log(LogLevel::LOG_INFO, message);
}

void Logger::warning(const std::string& message)
{
    log(LogLevel::LOG_WARNING, message);
}

void Logger::error(const std::string& message)
{
    log(LogLevel::LOG_ERROR, message);
}

void Logger::log(LogLevel level, const std::string& message)
{
    EnterCriticalSection(&m_criticalSection);

    // Check if we should log this level
    if (level < m_logLevel) {
        LeaveCriticalSection(&m_criticalSection);
        return;
    }

    std::string formattedMessage = formatMessage(level, message);

    // Write to file
    if (!m_logFile.empty()) {
        writeToFile(formattedMessage);
    }

    // Write to console
    if (m_consoleOutput) {
        writeToConsole(formattedMessage);
    }

    // Write to Windows Event Log
    if (m_eventLogOutput && m_eventLogHandle) {
        WORD eventType = EVENTLOG_INFORMATION_TYPE;
        switch (level) {
        case LogLevel::LOG_ERROR:
            eventType = EVENTLOG_ERROR_TYPE;
            break;
        case LogLevel::LOG_WARNING:
            eventType = EVENTLOG_WARNING_TYPE;
            break;
        default:
            eventType = EVENTLOG_INFORMATION_TYPE;
            break;
        }
        writeToEventLog(message, eventType);
    }

    LeaveCriticalSection(&m_criticalSection);
}

void Logger::writeToFile(const std::string& message)
{
    if (m_fileStream.is_open()) {
        m_fileStream << message << std::endl;
        m_fileStream.flush();
    }
}

void Logger::writeToConsole(const std::string& message)
{
    std::cout << message << std::endl;
}

void Logger::writeToEventLog(const std::string& message, WORD eventType)
{
    if (!m_eventLogHandle) {
        return;
    }

    // Convert to wide string
    int wideSize = MultiByteToWideChar(CP_UTF8, 0, message.c_str(), -1, nullptr, 0);
    if (wideSize > 0) {
        std::vector<wchar_t> wideMessage(wideSize);
        MultiByteToWideChar(CP_UTF8, 0, message.c_str(), -1, wideMessage.data(), wideSize);

        LPCWSTR strings[] = { wideMessage.data() };
        ReportEventW(
            m_eventLogHandle,
            eventType,
            0,
            0,
            nullptr,
            1,
            0,
            strings,
            nullptr
        );
    }
}

std::string Logger::formatMessage(LogLevel level, const std::string& message)
{
    std::ostringstream oss;
    oss << "[" << getCurrentTimestamp() << "] "
        << "[" << levelToString(level) << "] "
        << message;
    return oss.str();
}

std::string Logger::getCurrentTimestamp()
{
    SYSTEMTIME st;
    GetLocalTime(&st);

    std::ostringstream oss;
    oss << std::setfill('0')
        << std::setw(4) << st.wYear << "-"
        << std::setw(2) << st.wMonth << "-"
        << std::setw(2) << st.wDay << " "
        << std::setw(2) << st.wHour << ":"
        << std::setw(2) << st.wMinute << ":"
        << std::setw(2) << st.wSecond << "."
        << std::setw(3) << st.wMilliseconds;

    return oss.str();
}

std::string Logger::levelToString(LogLevel level)
{
    switch (level) {
    case LogLevel::LOG_DEBUG:   return "DEBUG";
    case LogLevel::LOG_INFO:    return "INFO";
    case LogLevel::LOG_WARNING: return "WARN";
    case LogLevel::LOG_ERROR:   return "ERROR";
    default:                    return "UNKNOWN";
    }
}
