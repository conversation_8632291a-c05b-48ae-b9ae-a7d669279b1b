cmake_minimum_required(VERSION 3.20)
project(SuperBotClientTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find vcpkg packages
find_package(lz4 CONFIG REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Test executable
add_executable(test_lz4_compression
    test_lz4_compression.cpp
    src/Compression.cpp
    src/DataIntegrity.cpp
    src/Logger.cpp
)

# Link libraries
target_link_libraries(test_lz4_compression PRIVATE lz4::lz4)

# Windows specific settings
if(WIN32)
    target_link_libraries(test_lz4_compression PRIVATE ws2_32)
endif()
