@echo off
echo ========================================
echo SuperBot Client LZ4 Compression Test
echo ========================================

echo Building LZ4 compression test...
if not exist build-test mkdir build-test
cd build-test
cmake -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake -DCMAKE_BUILD_TYPE=Debug -f ../CMakeLists_test.txt ..
cmake --build . --config Debug
cd ..

if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed!
    exit /b 1
)

echo ✅ Build successful!
echo.
echo Running LZ4 compression test...
echo ========================================
build-test\Debug\test_lz4_compression.exe

if %ERRORLEVEL% neq 0 (
    echo ❌ Tests failed!
    exit /b 1
)

echo.
echo ✅ All tests completed successfully!
echo ========================================
