#pragma once

#include "Common.h"
#include "Compression.h"

class ScreenCapture
{
public:
    ScreenCapture();
    ~ScreenCapture();

    // Initialization
    bool initialize();
    void shutdown();

    // Capture control
    bool start();
    void stop();
    bool isCapturing() const { return m_capturing; }

    // Frame processing
    void processFrame();

    // Configuration
    void setFrameRate(int fps) { m_frameRate = fps; }
    void setQuality(int quality) { m_quality = quality; }
    void setFormat(ScreenFormat format) { m_format = format; }
    void setCompressionEnabled(bool enabled) { m_compressionEnabled = enabled; }
    void setDeltaFramesEnabled(bool enabled) { m_deltaFramesEnabled = enabled; }

    // Image quality control
    void setImageScalePercent(float percent) { m_imageScalePercent = percent; }
    float getImageScalePercent() const { return m_imageScalePercent; }
    void setImageResizeEnabled(bool enabled) { m_imageResizeEnabled = enabled; }
    bool isImageResizeEnabled() const { return m_imageResizeEnabled; }

    // Monitor management
    std::vector<MonitorInfo> getMonitors() const;
    void setActiveMonitor(int monitorId) { m_activeMonitor = monitorId; }
    void setCaptureRegion(const Rect& region);
    void resetCaptureRegion();

    // Performance settings
    void setMaxFrameSize(int maxSize) { m_maxFrameSize = maxSize; }
    void setAdaptiveQuality(bool enabled) { m_adaptiveQuality = enabled; }
    void setHardwareAcceleration(bool enabled) { m_hardwareAcceleration = enabled; }

    // Statistics
    int getCurrentFrameRate() const { return m_currentFrameRate; }
    int getAverageFrameSize() const { return m_averageFrameSize; }
    double getCpuUsage() const { return m_cpuUsage; }

    // Callbacks
    void setFrameReadyCallback(std::function<void(const ScreenFrame&)> callback);
    void setErrorCallback(std::function<void(const std::string&)> callback);

private:
    // Platform-specific capture methods
    bool initializePlatformCapture();
    void cleanupPlatformCapture();
    ScreenFrame captureScreen();
    ScreenFrame captureMonitor(int monitorId);
    ScreenFrame captureRegion(const Rect& region);

    // Frame processing
    ScreenFrame processFrame(const std::vector<uint8_t>& rawData, const Size& size);
    std::vector<uint8_t> compressFrame(const std::vector<uint8_t>& data, ScreenFormat format);
    ScreenFrame createDeltaFrame(const ScreenFrame& current, const ScreenFrame& previous);
    bool shouldSendFrame(const ScreenFrame& frame);

    // Quality management
    void adjustQuality();
    int calculateOptimalQuality(int frameSize, int targetSize);
    void updateFrameRateStats();

    // Monitor detection
    void detectMonitors();
    void updateMonitorInfo();

    // Performance monitoring
    void updatePerformanceStats();
    double calculateCpuUsage();

    // Data conversion
    std::vector<uint8_t> convertBGRAtoRGB24(const std::vector<uint8_t>& bgraData, int width, int height);
    std::vector<uint8_t> convertBGRAtoBGR24(const std::vector<uint8_t>& bgraData, int width, int height);

    // Image processing
    std::vector<uint8_t> resizeImage(const std::vector<uint8_t>& imageData, int originalWidth, int originalHeight, int newWidth, int newHeight);
    std::vector<uint8_t> resizeImageBy50Percent(const std::vector<uint8_t>& imageData, int originalWidth, int originalHeight, int& newWidth, int& newHeight);
    std::vector<uint8_t> resizeImageByPercent(const std::vector<uint8_t>& imageData, int originalWidth, int originalHeight, float scalePercent, int& newWidth, int& newHeight);

    // Image saving functionality removed

    // Capture settings
    bool m_capturing;
    int m_frameRate;
    int m_quality;
    ScreenFormat m_format;
    bool m_compressionEnabled;
    bool m_deltaFramesEnabled;
    bool m_adaptiveQuality;
    bool m_hardwareAcceleration;

    // Image resize settings
    bool m_imageResizeEnabled;
    float m_imageScalePercent;

    // Compression
    std::unique_ptr<Compression::CompressionManager> m_compressionManager;

    // Monitor and region settings
    std::vector<MonitorInfo> m_monitors;
    int m_activeMonitor;
    Rect m_captureRegion;
    bool m_useCustomRegion;

    // Performance settings
    int m_maxFrameSize;
    int m_targetFrameRate;
    int m_currentFrameRate;
    int m_averageFrameSize;
    double m_cpuUsage;

    // Frame management
    ScreenFrame m_lastFrame;
    std::chrono::steady_clock::time_point m_lastCaptureTime;
    CRITICAL_SECTION m_captureCriticalSection;

    // Statistics
    int m_frameCount;
    int m_totalFrameSize;
    std::chrono::steady_clock::time_point m_lastStatsUpdate;
    std::chrono::steady_clock::time_point m_captureStartTime;

    // Callbacks
    std::function<void(const ScreenFrame&)> m_frameReadyCallback;
    std::function<void(const std::string&)> m_errorCallback;

    // Windows-specific data
    struct WindowsData;
    std::unique_ptr<WindowsData> m_windowsData;

    // Constants
    static constexpr int DEFAULT_FRAME_RATE = 30;
    static constexpr int DEFAULT_SCREEN_QUALITY = 80;
    static constexpr int MAX_FRAME_SIZE = 10 * 1024 * 1024;  // 10MB
    static constexpr int STATS_UPDATE_INTERVAL = 1000;  // 1 second
    static constexpr int QUALITY_ADJUSTMENT_THRESHOLD = 5;  // frames
};
