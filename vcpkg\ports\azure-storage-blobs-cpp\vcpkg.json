{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-storage-blobs-cpp", "version-semver": "12.13.0", "port-version": 1, "description": ["Microsoft Azure Storage Blobs SDK for C++", "This library provides Azure Storage Blobs SDK."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/storage/azure-storage-blobs", "license": "MIT", "dependencies": [{"name": "azure-storage-common-cpp", "default-features": false, "version>=": "12.8.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}