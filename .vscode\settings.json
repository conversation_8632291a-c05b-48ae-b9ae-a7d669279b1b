{"C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "C_Cpp.default.compilerPath": "", "C_Cpp.default.cStandard": "c17", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.includePath": ["${workspaceFolder}/include", "${workspaceFolder}/vcpkg/installed/x64-windows/include"], "C_Cpp.default.defines": ["_DEBUG", "DEBUG", "SUPERBOT_DEBUG", "ENABLE_LOGGING", "ENABLE_DETAILED_LOGGING", "ENABLE_PERFORMANCE_MONITORING", "WIN32_LEAN_AND_MEAN", "NOMINMAX", "_WIN32_WINNT=0x0A00", "WINVER=0x0A00", "_CRT_SECURE_NO_WARNINGS", "_WINSOCK_DEPRECATED_NO_WARNINGS"], "cmake.buildDirectory": "${workspaceFolder}/build-debug", "cmake.configureSettings": {"CMAKE_TOOLCHAIN_FILE": "${workspaceFolder}/vcpkg/scripts/buildsystems/vcpkg.cmake", "CMAKE_BUILD_TYPE": "Debug", "VCPKG_TARGET_TRIPLET": "x64-windows"}, "cmake.generator": "Visual Studio 17 2022", "cmake.preferredGenerators": ["Visual Studio 17 2022", "Visual Studio 16 2019", "MinGW Makefiles", "Ninja"], "cmake.buildArgs": ["--config", "Debug"], "cmake.parallelJobs": 0, "cmake.buildBeforeRun": true, "cmake.clearOutputBeforeBuild": true, "files.associations": {"*.h": "cpp", "*.hpp": "cpp", "*.cpp": "cpp", "*.c": "c"}, "files.exclude": {"**/build": true, "**/build-debug": true, "**/vcpkg/buildtrees": true, "**/vcpkg/downloads": true, "**/vcpkg/packages": true, "**/.vs": true, "**/*.user": true}, "search.exclude": {"**/build": true, "**/build-debug": true, "**/vcpkg/buildtrees": true, "**/vcpkg/downloads": true, "**/vcpkg/packages": true}, "editor.formatOnSave": true, "editor.insertSpaces": true, "editor.tabSize": 4, "editor.detectIndentation": false, "[cpp]": {"editor.defaultFormatter": "ms-vscode.cpptools", "editor.formatOnSave": true, "editor.tabSize": 4}, "[c]": {"editor.defaultFormatter": "ms-vscode.cpptools", "editor.formatOnSave": true, "editor.tabSize": 4}, "debug.allowBreakpointsEverywhere": true, "debug.showBreakpointsInOverviewRuler": true, "debug.showInlineBreakpointCandidates": true, "terminal.integrated.defaultProfile.windows": "Command Prompt"}