#include "NetworkClient.h"
#include "Logger.h"
#include "Compression.h"

NetworkClient::NetworkClient()
    : m_serverSocket(INVALID_SOCKET), m_clientSocket(INVALID_SOCKET), m_port(DEFAULT_PORT), m_maxConnections(1), m_heartbeatInterval(30000), m_compressionEnabled(true), m_encryptionEnabled(true), m_nextSequenceNumber(1), m_expectedSequenceNumber(1), m_bytesSent(0), m_bytesReceived(0), m_messagesSent(0), m_messagesReceived(0), m_authenticated(false), m_running(false)
{
    m_receiveBuffer.reserve(RECEIVE_BUFFER_SIZE);
    InitializeCriticalSection(&m_sendCriticalSection);
}

NetworkClient::~NetworkClient()
{
    shutdown();
    DeleteCriticalSection(&m_sendCriticalSection);
}

bool NetworkClient::initialize(int port)
{
    m_port = port;
    m_compressionManager = std::make_unique<CompressionManager>();
    LOG_INFO("Initializing network client on port " + std::to_string(port));
    return true;
}

void NetworkClient::shutdown()
{
    LOG_INFO("Shutting down network client");

    m_running = false;
    stopServer();
    closeClientConnection();
    closeServerSocket();
}

bool NetworkClient::startServer()
{
    LOG_INFO("Starting network server");

    if (!createServerSocket())
    {
        LOG_ERROR("Failed to create server socket");
        return false;
    }

    m_running = true;
    LOG_INFO("Network server started successfully");
    return true;
}

void NetworkClient::stopServer()
{
    LOG_INFO("Stopping network server");
    m_running = false;
    closeServerSocket();
}

bool NetworkClient::isListening() const
{
    return m_serverSocket != INVALID_SOCKET;
}

int NetworkClient::serverPort() const
{
    return m_port;
}

bool NetworkClient::hasActiveConnection() const
{
    return m_clientSocket != INVALID_SOCKET;
}

std::string NetworkClient::clientAddress() const
{
    return m_clientAddress;
}

void NetworkClient::disconnectClient()
{
    LOG_INFO("Disconnecting client");
    closeClientConnection();

    if (m_clientDisconnectedCallback)
    {
        m_clientDisconnectedCallback();
    }
}

void NetworkClient::processEvents()
{
    if (!m_running || m_serverSocket == INVALID_SOCKET)
    {
        return;
    }

    // Check for new connections
    if (m_clientSocket == INVALID_SOCKET)
    {
        acceptConnection();
    }

    // Process incoming data
    if (m_clientSocket != INVALID_SOCKET)
    {
        processIncomingData();
    }
}

void NetworkClient::sendMessage(MessageType type, const std::vector<uint8_t> &payload, MessageFlags flags)
{
    if (m_clientSocket == INVALID_SOCKET)
    {
        return;
    }

    EnterCriticalSection(&m_sendCriticalSection);

    auto message = createMessage(type, payload, flags);
    int expectedSize = static_cast<int>(message.size());

    LOG_DEBUG("Attempting to send message: type=" + std::to_string(static_cast<int>(type)) +
              ", expected size=" + std::to_string(expectedSize) + " bytes");

    int result = sendData(message.data(), expectedSize);

    if (result == expectedSize)
    {
        m_bytesSent += result;
        m_messagesSent++;
        LOG_DEBUG("Message sent successfully: " + std::to_string(result) + " bytes");
    }
    else if (result > 0)
    {
        m_bytesSent += result;
        LOG_WARNING("Partial message sent: " + std::to_string(result) + "/" + std::to_string(expectedSize) + " bytes");
    }
    else
    {
        LOG_ERROR("Failed to send message: type=" + std::to_string(static_cast<int>(type)) +
                  ", size=" + std::to_string(expectedSize) + " bytes");
    }

    LeaveCriticalSection(&m_sendCriticalSection);
}

void NetworkClient::sendScreenFrame(const ScreenFrame &frame)
{
    if (m_clientSocket == INVALID_SOCKET)
    {
        LOG_WARNING("Cannot send screen frame: no client connected");
        return;
    }

    // Validate frame before sending
    if (!DataIntegrity::validateScreenFrame(frame))
    {
        LOG_ERROR("Invalid screen frame, skipping send");
        return;
    }

    LOG_DEBUG("Sending validated screen frame: " + std::to_string(frame.region.width) + "x" +
              std::to_string(frame.region.height) + ", format=" + std::to_string(static_cast<int>(frame.format)) +
              ", data size: " + std::to_string(frame.data.size()) + ", compressed=" + (frame.isCompressed ? "yes" : "no") +
              ", originalSize=" + std::to_string(frame.originalSize));

    // Use improved serialization with data integrity
    std::vector<uint8_t> payload = DataIntegrity::serializeScreenFrame(frame);

    // Set compression flag based on actual compression status
    MessageFlags flags = MessageFlags::NONE;
    if (frame.isCompressed)
    {
        flags = MessageFlags::COMPRESSED;
        LOG_DEBUG("Frame is compressed, setting compression flag");
    }

    // Debug struct sizes (only once)
    static bool debugPrinted = false;
    if (!debugPrinted)
    {
        DataIntegrity::debugStructSizes();
        debugPrinted = true;
    }

    // Calculate total message size (header + payload)
    size_t totalMessageSize = sizeof(MessageHeader) + payload.size();
    LOG_INFO("=== CLIENT SENDING SCREEN FRAME ===");
    LOG_INFO("Payload size: " + std::to_string(payload.size()) + " bytes");
    LOG_INFO("Total message size (header + payload): " + std::to_string(totalMessageSize) + " bytes");
    LOG_INFO("Message header size: " + std::to_string(sizeof(MessageHeader)) + " bytes");
    LOG_INFO("===================================");

    // Send the message with appropriate flags
    sendMessage(MessageType::SCREEN_FRAME, payload, flags);

    // Log comprehensive data integrity summary for client side
    LOG_INFO("=== CLIENT FRAME DATA INTEGRITY SUMMARY ===");
    LOG_INFO("Original frame data size: " + std::to_string(frame.data.size()) + " bytes");
    LOG_INFO("Serialized payload size: " + std::to_string(payload.size()) + " bytes");
    LOG_INFO("Total message size: " + std::to_string(totalMessageSize) + " bytes");
    LOG_INFO("Frame dimensions: " + std::to_string(frame.region.width) + "x" + std::to_string(frame.region.height));
    LOG_INFO("Frame format: " + std::to_string(static_cast<int>(frame.format)));
    LOG_INFO("Monitor ID: " + std::to_string(frame.monitorId));
    LOG_INFO("Region: (" + std::to_string(frame.region.x) + ", " + std::to_string(frame.region.y) +
             ", " + std::to_string(frame.region.width) + ", " + std::to_string(frame.region.height) + ")");
    LOG_INFO("Is compressed: " + std::string(frame.isCompressed ? "yes" : "no"));
    LOG_INFO("Is full frame: " + std::string(frame.isFullFrame ? "yes" : "no"));
    LOG_INFO("Timestamp: " + std::to_string(frame.timestamp));
    LOG_INFO("Message flags: " + std::to_string(static_cast<int>(flags)));
    LOG_INFO("============================================");

    LOG_DEBUG("Screen frame sent successfully, serialized payload size: " + std::to_string(payload.size()) +
              ", original data size: " + std::to_string(frame.data.size()) +
              ", compressed: " + (frame.isCompressed ? "yes" : "no"));
}

void NetworkClient::sendHeartbeat()
{
    std::vector<uint8_t> payload;
    sendMessage(MessageType::HEARTBEAT, payload);
}

void NetworkClient::sendError(ErrorCode code, const std::string &message)
{
    std::vector<uint8_t> payload;
    // TODO: Serialize error data
    sendMessage(MessageType::ERROR_RESPONSE, payload);
}

void NetworkClient::sendQualityStatus(const QualityStatusMessage &status)
{
    if (!hasActiveConnection())
    {
        return;
    }

    std::vector<uint8_t> payload(sizeof(QualityStatusMessage));
    memcpy(payload.data(), &status, sizeof(QualityStatusMessage));

    sendMessage(MessageType::QUALITY_STATUS, payload);

    LOG_DEBUG("Sent quality status: scale=" + std::to_string(status.currentImageScale) +
              ", fps=" + std::to_string(status.currentFrameRate) +
              ", avgSize=" + std::to_string(status.averageFrameSize));
}

void NetworkClient::resetStatistics()
{
    m_bytesSent = 0;
    m_bytesReceived = 0;
    m_messagesSent = 0;
    m_messagesReceived = 0;
}

void NetworkClient::setClientConnectedCallback(std::function<void(const std::string &)> callback)
{
    m_clientConnectedCallback = callback;
}

void NetworkClient::setClientDisconnectedCallback(std::function<void()> callback)
{
    m_clientDisconnectedCallback = callback;
}

void NetworkClient::setMessageReceivedCallback(std::function<void(MessageType, const std::vector<uint8_t> &)> callback)
{
    m_messageReceivedCallback = callback;
}

void NetworkClient::setErrorCallback(std::function<void(ErrorCode, const std::string &)> callback)
{
    m_errorCallback = callback;
}

void NetworkClient::setQualityControlCallback(std::function<void(const QualityControlMessage &)> callback)
{
    m_qualityControlCallback = callback;
}

bool NetworkClient::createServerSocket()
{
    m_serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_serverSocket == INVALID_SOCKET)
    {
        LOG_ERROR("Failed to create socket: " + std::to_string(WSAGetLastError()));
        return false;
    }

    // Set socket options
    int optval = 1;
    setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEADDR, reinterpret_cast<const char *>(&optval), sizeof(optval));

    // Bind socket
    sockaddr_in serverAddr = {};
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(static_cast<u_short>(m_port));

    if (bind(m_serverSocket, reinterpret_cast<sockaddr *>(&serverAddr), sizeof(serverAddr)) == SOCKET_ERROR)
    {
        LOG_ERROR("Failed to bind socket: " + std::to_string(WSAGetLastError()));
        closeServerSocket();
        return false;
    }

    // Listen for connections
    if (listen(m_serverSocket, SOMAXCONN) == SOCKET_ERROR)
    {
        LOG_ERROR("Failed to listen on socket: " + std::to_string(WSAGetLastError()));
        closeServerSocket();
        return false;
    }

    // Set non-blocking mode
    u_long mode = 1;
    ioctlsocket(m_serverSocket, FIONBIO, &mode);

    return true;
}

void NetworkClient::closeServerSocket()
{
    if (m_serverSocket != INVALID_SOCKET)
    {
        closesocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
    }
}

bool NetworkClient::acceptConnection()
{
    sockaddr_in clientAddr = {};
    int clientAddrSize = sizeof(clientAddr);

    SOCKET clientSocket = accept(m_serverSocket, reinterpret_cast<sockaddr *>(&clientAddr), &clientAddrSize);
    if (clientSocket == INVALID_SOCKET)
    {
        int error = WSAGetLastError();
        if (error != WSAEWOULDBLOCK)
        {
            LOG_ERROR("Failed to accept connection: " + std::to_string(error));
        }
        return false;
    }

    // Store client information
    m_clientSocket = clientSocket;
    char *clientIP = inet_ntoa(clientAddr.sin_addr);
    m_clientAddress = std::string(clientIP) + ":" + std::to_string(ntohs(clientAddr.sin_port));

    // Set non-blocking mode for client socket
    u_long mode = 1;
    ioctlsocket(m_clientSocket, FIONBIO, &mode);

    LOG_INFO("Client connected from: " + m_clientAddress);

    if (m_clientConnectedCallback)
    {
        m_clientConnectedCallback(m_clientAddress);
    }

    return true;
}

void NetworkClient::closeClientConnection()
{
    if (m_clientSocket != INVALID_SOCKET)
    {
        closesocket(m_clientSocket);
        m_clientSocket = INVALID_SOCKET;
        m_clientAddress.clear();
        m_authenticated = false;
    }
}

void NetworkClient::processIncomingData()
{
    char buffer[4096];
    int bytesReceived = recv(m_clientSocket, buffer, sizeof(buffer), 0);

    if (bytesReceived > 0)
    {
        m_bytesReceived += bytesReceived;

        // Add to receive buffer
        m_receiveBuffer.insert(m_receiveBuffer.end(), buffer, buffer + bytesReceived);

        // Process complete messages
        while (m_receiveBuffer.size() >= sizeof(MessageHeader))
        {
            MessageHeader header;
            std::vector<uint8_t> payload;

            if (parseMessage(m_receiveBuffer, header, payload))
            {
                m_messagesReceived++;

                // Handle specific message types
                MessageType messageType = static_cast<MessageType>(header.type);

                if (messageType == MessageType::QUALITY_CONTROL && m_qualityControlCallback)
                {
                    // Parse quality control message
                    if (payload.size() >= sizeof(QualityControlMessage))
                    {
                        QualityControlMessage qualityMsg;
                        memcpy(&qualityMsg, payload.data(), sizeof(QualityControlMessage));

                        LOG_INFO("Received quality control: scale=" + std::to_string(qualityMsg.imageScalePercent) +
                                 ", resize=" + (qualityMsg.resizeEnabled ? "true" : "false") +
                                 ", fps=" + std::to_string(qualityMsg.frameRate));

                        m_qualityControlCallback(qualityMsg);
                    }
                }

                if (m_messageReceivedCallback)
                {
                    m_messageReceivedCallback(messageType, payload);
                }

                // Remove processed message from buffer
                size_t messageSize = sizeof(MessageHeader) + payload.size();
                m_receiveBuffer.erase(m_receiveBuffer.begin(), m_receiveBuffer.begin() + messageSize);
            }
            else
            {
                break; // Wait for more data
            }
        }
    }
    else if (bytesReceived == 0)
    {
        // Client disconnected
        LOG_INFO("Client disconnected gracefully");
        disconnectClient();
    }
    else
    {
        int error = WSAGetLastError();
        if (error != WSAEWOULDBLOCK)
        {
            LOG_ERROR("Receive error: " + std::to_string(error));
            disconnectClient();
        }
    }
}

bool NetworkClient::parseMessage(const std::vector<uint8_t> &data, MessageHeader &header, std::vector<uint8_t> &payload)
{
    if (data.size() < sizeof(MessageHeader))
    {
        return false;
    }

    // Copy header
    memcpy(&header, data.data(), sizeof(MessageHeader));

    // Validate header
    size_t payloadSize = header.length - sizeof(MessageHeader);
    if (!DataIntegrity::validateMessageHeader(header, payloadSize))
    {
        return false;
    }

    // Check if we have the complete message
    if (data.size() < header.length)
    {
        return false;
    }

    // Extract payload
    if (payloadSize > 0)
    {
        payload.resize(payloadSize);
        memcpy(payload.data(), data.data() + sizeof(MessageHeader), payloadSize);

        // Validate checksum
        uint32_t calculatedChecksum = DataIntegrity::calculateCRC32(payload.data(), payload.size());
        if (header.checksum != calculatedChecksum)
        {
            LOG_ERROR("Checksum mismatch: expected 0x" + std::to_string(header.checksum) +
                      ", calculated 0x" + std::to_string(calculatedChecksum));
            return false;
        }

        LOG_DEBUG("Message validated: type=" + std::to_string(header.type) +
                  ", length=" + std::to_string(header.length) +
                  ", checksum=0x" + std::to_string(header.checksum));
    }

    return true;
}

std::vector<uint8_t> NetworkClient::createMessage(MessageType type, const std::vector<uint8_t> &payload, MessageFlags flags)
{
    MessageHeader header = {};
    header.magic = PROTOCOL_MAGIC;
    header.length = static_cast<uint32_t>(sizeof(MessageHeader) + payload.size());
    header.type = static_cast<uint16_t>(type);
    header.sequence = m_nextSequenceNumber++;
    header.flags = static_cast<uint16_t>(flags);
    header.version = PROTOCOL_VERSION_NUM;
    header.reserved = 0;

    // Calculate checksum of payload
    header.checksum = payload.empty() ? 0 : DataIntegrity::calculateCRC32(payload.data(), payload.size());

    std::vector<uint8_t> message(sizeof(MessageHeader) + payload.size());
    memcpy(message.data(), &header, sizeof(MessageHeader));
    if (!payload.empty())
    {
        memcpy(message.data() + sizeof(MessageHeader), payload.data(), payload.size());
    }

    LOG_DEBUG("Created message: type=" + std::to_string(static_cast<int>(type)) +
              ", length=" + std::to_string(header.length) +
              ", payload size=" + std::to_string(payload.size()) +
              ", checksum=0x" + std::to_string(header.checksum) +
              ", flags=" + std::to_string(static_cast<int>(flags)));

    return message;
}

std::vector<uint8_t> NetworkClient::compressData(const std::vector<uint8_t> &data)
{
    // TODO: Implement compression
    return data;
}

std::vector<uint8_t> NetworkClient::decompressData(const std::vector<uint8_t> &data)
{
    // TODO: Implement decompression
    return data;
}

std::vector<uint8_t> NetworkClient::encryptData(const std::vector<uint8_t> &data)
{
    // TODO: Implement encryption
    return data;
}

std::vector<uint8_t> NetworkClient::decryptData(const std::vector<uint8_t> &data)
{
    // TODO: Implement decryption
    return data;
}

int NetworkClient::sendData(const void *data, int size)
{
    if (m_clientSocket == INVALID_SOCKET)
    {
        return -1;
    }

    const char *buffer = static_cast<const char *>(data);
    int totalSent = 0;
    int remaining = size;

    while (remaining > 0)
    {
        int sent = send(m_clientSocket, buffer + totalSent, remaining, 0);

        if (sent == SOCKET_ERROR)
        {
            int error = WSAGetLastError();
            if (error == WSAEWOULDBLOCK)
            {
                // Socket buffer is full, wait a bit and try again
                Sleep(1);
                continue;
            }
            else
            {
                LOG_ERROR("Send error: " + std::to_string(error) +
                          ", sent " + std::to_string(totalSent) + "/" + std::to_string(size) + " bytes");
                return totalSent > 0 ? totalSent : -1;
            }
        }
        else if (sent == 0)
        {
            // Connection closed
            LOG_WARNING("Connection closed during send, sent " + std::to_string(totalSent) + "/" + std::to_string(size) + " bytes");
            return totalSent;
        }

        totalSent += sent;
        remaining -= sent;

        LOG_DEBUG("Sent " + std::to_string(sent) + " bytes, total: " + std::to_string(totalSent) + "/" + std::to_string(size));
    }

    LOG_DEBUG("Successfully sent all " + std::to_string(totalSent) + " bytes");
    return totalSent;
}

int NetworkClient::receiveData(void *buffer, int size)
{
    if (m_clientSocket == INVALID_SOCKET)
    {
        return -1;
    }

    return recv(m_clientSocket, static_cast<char *>(buffer), size, 0);
}

bool NetworkClient::validateClient()
{
    // TODO: Implement client validation
    return true;
}

bool NetworkClient::performHandshake()
{
    // TODO: Implement handshake protocol
    return true;
}
