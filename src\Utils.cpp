#include "Common.h"
#include <shlobj.h>

std::string getLogFilePath()
{
    // Try to get a writable directory for log files
    char appDataPath[MAX_PATH];
    HRESULT hr = SHGetFolderPathA(nullptr, CSIDL_COMMON_APPDATA, nullptr, 0, appDataPath);
    
    if (SUCCEEDED(hr)) {
        std::string logDir = std::string(appDataPath) + "\\SuperBot";
        
        // Create directory if it doesn't exist
        CreateDirectoryA(logDir.c_str(), nullptr);
        
        return logDir + "\\SuperBotClient.log";
    }
    
    // Fallback to current directory
    return "SuperBotClient.log";
}
