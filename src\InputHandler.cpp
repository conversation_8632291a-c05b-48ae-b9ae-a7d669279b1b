#include "InputHandler.h"
#include "Logger.h"

// Windows-specific data structure
struct InputHandler::WindowsInputData {
    POINT lastCursorPos;
    BY<PERSON> keyboardState[256];
    bool initialized;
};

InputHandler::InputHandler()
    : m_inputEnabled(true)
    , m_inputFiltering(false)
    , m_mouseAcceleration(true)
    , m_keyRepeatEnabled(true)
    , m_inputDelay(DEFAULT_INPUT_DELAY)
    , m_keyRepeatDelay(DEFAULT_KEY_REPEAT_DELAY)
    , m_keyRepeatRate(DEFAULT_KEY_REPEAT_RATE)
    , m_useInputRegion(false)
    , m_currentModifiers(0)
    , m_lastRepeatedKey(0)
    , m_inputEventsProcessed(0)
    , m_inputEventsBlocked(0)
    , m_windowsData(std::make_unique<WindowsInputData>())
{
    m_lastInputTime = std::chrono::steady_clock::now();
    m_lastKeyRepeatTime = std::chrono::steady_clock::now();
    m_windowsData->initialized = false;
}

InputHandler::~InputHandler()
{
    shutdown();
}

bool InputHandler::initialize()
{
    LOG_INFO("Initializing input handler");
    
    if (!initializePlatformInput()) {
        LOG_ERROR("Failed to initialize platform-specific input");
        return false;
    }
    
    LOG_INFO("Input handler initialized successfully");
    return true;
}

void InputHandler::shutdown()
{
    LOG_INFO("Shutting down input handler");
    cleanupPlatformInput();
}

void InputHandler::processEvents()
{
    if (!m_inputEnabled) {
        return;
    }
    
    // Process input queue
    processInputQueue();
    
    // Handle key repeat
    auto now = std::chrono::steady_clock::now();
    if (m_keyRepeatEnabled && m_lastRepeatedKey != 0) {
        auto timeSinceLastRepeat = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastKeyRepeatTime);
        if (timeSinceLastRepeat.count() >= m_keyRepeatDelay) {
            simulateKeyPress(m_lastRepeatedKey, true);
            m_lastKeyRepeatTime = now;
        }
    }
}

void InputHandler::handleMouseMove(const Point& position)
{
    if (!validateMouseInput(position)) {
        return;
    }
    
    queueInputEvent([this, position]() {
        return simulateMouseMove(position);
    });
}

void InputHandler::handleMouseClick(const Point& position, MouseButton button, bool pressed)
{
    if (!validateMouseInput(position)) {
        return;
    }
    
    queueInputEvent([this, position, button, pressed]() {
        return simulateMouseClick(position, button, pressed);
    });
    
    updateMouseButtonState(button, pressed);
}

void InputHandler::handleMouseScroll(const Point& position, int delta)
{
    if (!validateMouseInput(position)) {
        return;
    }
    
    queueInputEvent([this, position, delta]() {
        return simulateMouseScroll(position, delta);
    });
}

void InputHandler::setMousePosition(const Point& position)
{
    m_lastMousePosition = position;
}

Point InputHandler::getMousePosition() const
{
    POINT cursorPos;
    GetCursorPos(&cursorPos);
    return Point(cursorPos.x, cursorPos.y);
}

void InputHandler::handleKeyPress(uint32_t keyCode, uint32_t modifiers)
{
    if (!validateKeyInput(keyCode)) {
        return;
    }
    
    queueInputEvent([this, keyCode]() {
        return simulateKeyPress(keyCode, true);
    });
    
    updateKeyState(keyCode, true);
    updateModifierState(modifiers);
    
    if (m_keyRepeatEnabled) {
        m_lastRepeatedKey = keyCode;
        m_lastKeyRepeatTime = std::chrono::steady_clock::now();
    }
}

void InputHandler::handleKeyRelease(uint32_t keyCode, uint32_t modifiers)
{
    if (!validateKeyInput(keyCode)) {
        return;
    }
    
    queueInputEvent([this, keyCode]() {
        return simulateKeyPress(keyCode, false);
    });
    
    updateKeyState(keyCode, false);
    updateModifierState(modifiers);
    
    if (m_lastRepeatedKey == keyCode) {
        m_lastRepeatedKey = 0;
    }
}

void InputHandler::handleTextInput(const std::string& text)
{
    queueInputEvent([this, text]() {
        return simulateTextInput(text);
    });
}

void InputHandler::sendKeySequence(const std::vector<uint32_t>& keyCodes)
{
    for (uint32_t keyCode : keyCodes) {
        handleKeyPress(keyCode, 0);
        Sleep(10); // Small delay between keys
        handleKeyRelease(keyCode, 0);
    }
}

void InputHandler::addBlockedKey(uint32_t keyCode)
{
    m_blockedKeys.insert(keyCode);
}

void InputHandler::removeBlockedKey(uint32_t keyCode)
{
    m_blockedKeys.erase(keyCode);
}

void InputHandler::setAllowedInputRegion(const Rect& region)
{
    m_allowedInputRegion = region;
    m_useInputRegion = true;
}

void InputHandler::clearAllowedInputRegion()
{
    m_useInputRegion = false;
}

void InputHandler::setClipboardText(const std::string& text)
{
    if (OpenClipboard(nullptr)) {
        EmptyClipboard();
        
        // Convert to wide string
        int wideSize = MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, nullptr, 0);
        if (wideSize > 0) {
            HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, wideSize * sizeof(wchar_t));
            if (hMem) {
                wchar_t* pMem = static_cast<wchar_t*>(GlobalLock(hMem));
                MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, pMem, wideSize);
                GlobalUnlock(hMem);
                SetClipboardData(CF_UNICODETEXT, hMem);
            }
        }
        
        CloseClipboard();
    }
}

std::string InputHandler::getClipboardText() const
{
    std::string result;
    
    if (OpenClipboard(nullptr)) {
        HANDLE hData = GetClipboardData(CF_UNICODETEXT);
        if (hData) {
            wchar_t* pData = static_cast<wchar_t*>(GlobalLock(hData));
            if (pData) {
                // Convert to UTF-8
                int utf8Size = WideCharToMultiByte(CP_UTF8, 0, pData, -1, nullptr, 0, nullptr, nullptr);
                if (utf8Size > 0) {
                    std::vector<char> utf8Buffer(utf8Size);
                    WideCharToMultiByte(CP_UTF8, 0, pData, -1, utf8Buffer.data(), utf8Size, nullptr, nullptr);
                    result = utf8Buffer.data();
                }
                GlobalUnlock(hData);
            }
        }
        CloseClipboard();
    }
    
    return result;
}

void InputHandler::setClipboardImage(const std::vector<uint8_t>& imageData)
{
    // TODO: Implement clipboard image setting
}

std::vector<uint8_t> InputHandler::getClipboardImage() const
{
    // TODO: Implement clipboard image getting
    return {};
}

bool InputHandler::isKeyPressed(uint32_t keyCode) const
{
    return m_pressedKeys.find(keyCode) != m_pressedKeys.end();
}

bool InputHandler::isMouseButtonPressed(MouseButton button) const
{
    return m_pressedMouseButtons.find(button) != m_pressedMouseButtons.end();
}

uint32_t InputHandler::getCurrentModifiers() const
{
    return m_currentModifiers;
}

void InputHandler::resetStatistics()
{
    m_inputEventsProcessed = 0;
    m_inputEventsBlocked = 0;
}

void InputHandler::processMouseEvent(const MouseEvent& event)
{
    switch (event.type) {
    case InputType::MOUSE_MOVE:
        handleMouseMove(event.position);
        break;
    case InputType::MOUSE_CLICK:
        handleMouseClick(event.position, event.button, true);
        break;
    case InputType::MOUSE_SCROLL:
        handleMouseScroll(event.position, event.scrollDelta);
        break;
    }
}

void InputHandler::processKeyboardEvent(const KeyboardEvent& event)
{
    switch (event.type) {
    case InputType::KEY_DOWN:
        handleKeyPress(event.keyCode, event.modifiers);
        break;
    case InputType::KEY_UP:
        handleKeyRelease(event.keyCode, event.modifiers);
        break;
    }
}

void InputHandler::setInputProcessedCallback(std::function<void(const std::string&)> callback)
{
    m_inputProcessedCallback = callback;
}

void InputHandler::setInputBlockedCallback(std::function<void(const std::string&)> callback)
{
    m_inputBlockedCallback = callback;
}

void InputHandler::setInputErrorCallback(std::function<void(const std::string&)> callback)
{
    m_inputErrorCallback = callback;
}

bool InputHandler::initializePlatformInput()
{
    GetCursorPos(&m_windowsData->lastCursorPos);
    GetKeyboardState(m_windowsData->keyboardState);
    m_windowsData->initialized = true;
    return true;
}

void InputHandler::cleanupPlatformInput()
{
    m_windowsData->initialized = false;
}

bool InputHandler::simulateMouseMove(const Point& position)
{
    SetCursorPos(position.x, position.y);
    m_lastMousePosition = position;
    return true;
}

bool InputHandler::simulateMouseClick(const Point& position, MouseButton button, bool pressed)
{
    SetCursorPos(position.x, position.y);
    
    DWORD flags = 0;
    switch (button) {
    case MouseButton::LEFT:
        flags = pressed ? MOUSEEVENTF_LEFTDOWN : MOUSEEVENTF_LEFTUP;
        break;
    case MouseButton::RIGHT:
        flags = pressed ? MOUSEEVENTF_RIGHTDOWN : MOUSEEVENTF_RIGHTUP;
        break;
    case MouseButton::MIDDLE:
        flags = pressed ? MOUSEEVENTF_MIDDLEDOWN : MOUSEEVENTF_MIDDLEUP;
        break;
    }
    
    if (flags != 0) {
        mouse_event(flags, position.x, position.y, 0, 0);
        return true;
    }
    
    return false;
}

bool InputHandler::simulateMouseScroll(const Point& position, int delta)
{
    SetCursorPos(position.x, position.y);
    mouse_event(MOUSEEVENTF_WHEEL, position.x, position.y, delta, 0);
    return true;
}

bool InputHandler::simulateKeyPress(uint32_t keyCode, bool pressed)
{
    DWORD flags = pressed ? 0 : KEYEVENTF_KEYUP;
    keybd_event(static_cast<BYTE>(keyCode), 0, flags, 0);
    return true;
}

bool InputHandler::simulateTextInput(const std::string& text)
{
    // Convert to wide string and send as Unicode input
    int wideSize = MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, nullptr, 0);
    if (wideSize > 0) {
        std::vector<wchar_t> wideText(wideSize);
        MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, wideText.data(), wideSize);
        
        for (wchar_t ch : wideText) {
            if (ch == 0) break;
            
            INPUT input = {};
            input.type = INPUT_KEYBOARD;
            input.ki.wVk = 0;
            input.ki.wScan = ch;
            input.ki.dwFlags = KEYEVENTF_UNICODE;
            
            SendInput(1, &input, sizeof(INPUT));
        }
        return true;
    }
    
    return false;
}

bool InputHandler::validateMouseInput(const Point& position)
{
    if (!m_inputEnabled) {
        return false;
    }
    
    if (m_useInputRegion) {
        return isPositionAllowed(position);
    }
    
    return true;
}

bool InputHandler::validateKeyInput(uint32_t keyCode)
{
    if (!m_inputEnabled) {
        return false;
    }
    
    return !isKeyBlocked(keyCode);
}

bool InputHandler::isKeyBlocked(uint32_t keyCode) const
{
    return m_blockedKeys.find(keyCode) != m_blockedKeys.end();
}

bool InputHandler::isPositionAllowed(const Point& position) const
{
    return position.x >= m_allowedInputRegion.x &&
           position.y >= m_allowedInputRegion.y &&
           position.x < m_allowedInputRegion.x + m_allowedInputRegion.width &&
           position.y < m_allowedInputRegion.y + m_allowedInputRegion.height;
}

void InputHandler::queueInputEvent(const std::function<bool()>& inputFunction)
{
    if (m_inputQueue.size() < INPUT_QUEUE_MAX_SIZE) {
        m_inputQueue.push(inputFunction);
    }
}

void InputHandler::processInputQueue()
{
    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastInput = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastInputTime);
    
    if (timeSinceLastInput.count() >= m_inputDelay && !m_inputQueue.empty()) {
        auto inputFunction = m_inputQueue.front();
        m_inputQueue.pop();
        
        if (inputFunction()) {
            m_inputEventsProcessed++;
            if (m_inputProcessedCallback) {
                m_inputProcessedCallback("Input event processed");
            }
        } else {
            m_inputEventsBlocked++;
            if (m_inputBlockedCallback) {
                m_inputBlockedCallback("Input event failed");
            }
        }
        
        m_lastInputTime = now;
    }
}

void InputHandler::clearInputQueue()
{
    while (!m_inputQueue.empty()) {
        m_inputQueue.pop();
    }
}

void InputHandler::updateKeyState(uint32_t keyCode, bool pressed)
{
    if (pressed) {
        m_pressedKeys.insert(keyCode);
    } else {
        m_pressedKeys.erase(keyCode);
    }
}

void InputHandler::updateMouseButtonState(MouseButton button, bool pressed)
{
    if (pressed) {
        m_pressedMouseButtons.insert(button);
    } else {
        m_pressedMouseButtons.erase(button);
    }
}

void InputHandler::updateModifierState(uint32_t modifiers)
{
    m_currentModifiers = modifiers;
}

void InputHandler::updateClipboard()
{
    // TODO: Implement clipboard monitoring
}

void InputHandler::handleClipboardChange()
{
    // TODO: Implement clipboard change handling
}

bool InputHandler::checkInputSecurity(const std::string& operation)
{
    // TODO: Implement security checks
    return true;
}

void InputHandler::logInputEvent(const std::string& event, bool blocked)
{
    if (blocked) {
        LOG_WARNING("Input blocked: " + event);
    } else {
        LOG_DEBUG("Input processed: " + event);
    }
}
