#pragma once

#include "Common.h"
#include <fstream>
#include <sstream>

enum class LogLevel {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARNING = 2,
    LOG_ERROR = 3
};

class Logger {
public:
    static Logger* instance();

    void setLogLevel(LogLevel level);
    void setLogFile(const std::string& filename);
    void enableConsoleOutput(bool enable);
    void enableEventLog(bool enable);

    void debug(const std::string& message);
    void info(const std::string& message);
    void warning(const std::string& message);
    void error(const std::string& message);

    void log(LogLevel level, const std::string& message);

public:
    Logger();
    ~Logger();

    void writeToFile(const std::string& message);
    void writeToConsole(const std::string& message);
    void writeToEventLog(const std::string& message, WORD eventType);
    std::string formatMessage(LogLevel level, const std::string& message);
    std::string getCurrentTimestamp();
    std::string levelToString(LogLevel level);

    static Logger* s_instance;
    LogLevel m_logLevel;
    std::string m_logFile;
    bool m_consoleOutput;
    bool m_eventLogOutput;
    CRITICAL_SECTION m_criticalSection;
    std::ofstream m_fileStream;
    HANDLE m_eventLogHandle;
};

// Convenience macros
#define LOG_DEBUG(msg) Logger::instance()->debug(msg)
#define LOG_INFO(msg) Logger::instance()->info(msg)
#define LOG_WARNING(msg) Logger::instance()->warning(msg)
#define LOG_ERROR(msg) Logger::instance()->error(msg)
