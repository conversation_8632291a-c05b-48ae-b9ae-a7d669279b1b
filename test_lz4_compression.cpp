#include "Common.h"
#include "Compression.h"
#include <iostream>
#include <vector>
#include <random>
#include <chrono>

// Test data generators
std::vector<uint8_t> generateRandomData(size_t size) {
    std::vector<uint8_t> data(size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    for (size_t i = 0; i < size; i++) {
        data[i] = static_cast<uint8_t>(dis(gen));
    }
    return data;
}

std::vector<uint8_t> generateRepeatingData(size_t size) {
    std::vector<uint8_t> data(size);
    uint8_t pattern[] = {0x12, 0x34, 0x56, 0x78};

    for (size_t i = 0; i < size; i++) {
        data[i] = pattern[i % 4];
    }
    return data;
}

std::vector<uint8_t> generateScreenLikeData(int width, int height) {
    size_t size = width * height * 3; // BGR24
    std::vector<uint8_t> data(size);

    // Simulate screen data with some patterns
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            size_t idx = (y * width + x) * 3;

            // Create some patterns like a desktop
            if (y < height / 4) {
                // Top area - blue gradient
                data[idx] = static_cast<uint8_t>(255 - (x * 255 / width));     // B
                data[idx + 1] = static_cast<uint8_t>(128);                     // G
                data[idx + 2] = static_cast<uint8_t>(64);                      // R
            } else if (y < height / 2) {
                // Middle area - green
                data[idx] = 64;                                                // B
                data[idx + 1] = static_cast<uint8_t>(200 - (x * 100 / width)); // G
                data[idx + 2] = 64;                                            // R
            } else {
                // Bottom area - repeating pattern
                data[idx] = static_cast<uint8_t>((x + y) % 256);               // B
                data[idx + 1] = static_cast<uint8_t>((x * 2) % 256);           // G
                data[idx + 2] = static_cast<uint8_t>((y * 2) % 256);           // R
            }
        }
    }
    return data;
}

// Test functions
bool testLZ4Compression(const std::string& testName, const std::vector<uint8_t>& testData) {
    std::cout << "\n=== Testing " << testName << " ===" << std::endl;
    std::cout << "Original data size: " << testData.size() << " bytes" << std::endl;

    // Test compression
    Compression::LZ4Compressor compressor;
    auto startTime = std::chrono::high_resolution_clock::now();
    auto result = compressor.compress(testData);
    auto endTime = std::chrono::high_resolution_clock::now();

    auto compressionTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);

    if (!result.success) {
        std::cout << "❌ Compression FAILED" << std::endl;
        return false;
    }

    std::cout << "✅ Compression successful:" << std::endl;
    std::cout << "  Compressed size: " << result.compressedSize << " bytes" << std::endl;
    std::cout << "  Compression ratio: " << (result.ratio * 100) << "%" << std::endl;
    std::cout << "  Compression time: " << compressionTime.count() << " μs" << std::endl;
    std::cout << "  Space saved: " << (result.originalSize - result.compressedSize) << " bytes" << std::endl;

    // Test decompression
    startTime = std::chrono::high_resolution_clock::now();
    auto decompressed = compressor.decompress(result.data, result.originalSize);
    endTime = std::chrono::high_resolution_clock::now();

    auto decompressionTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);

    if (decompressed.empty()) {
        std::cout << "❌ Decompression FAILED" << std::endl;
        return false;
    }

    std::cout << "✅ Decompression successful:" << std::endl;
    std::cout << "  Decompressed size: " << decompressed.size() << " bytes" << std::endl;
    std::cout << "  Decompression time: " << decompressionTime.count() << " μs" << std::endl;

    // Verify data integrity
    if (decompressed.size() != testData.size()) {
        std::cout << "❌ Size mismatch: expected " << testData.size() << ", got " << decompressed.size() << std::endl;
        return false;
    }

    bool dataMatch = true;
    for (size_t i = 0; i < testData.size(); i++) {
        if (testData[i] != decompressed[i]) {
            std::cout << "❌ Data mismatch at byte " << i << ": expected " << (int)testData[i]
                      << ", got " << (int)decompressed[i] << std::endl;
            dataMatch = false;
            break;
        }
    }

    if (dataMatch) {
        std::cout << "✅ Data integrity verified - all bytes match!" << std::endl;
    }

    return dataMatch;
}

bool testScreenFrameSerialization() {
    std::cout << "\n=== Testing ScreenFrame Serialization with Compression ===" << std::endl;

    // Create a test screen frame
    ScreenFrame frame;
    frame.monitorId = 1;
    frame.region = Rect(0, 0, 800, 600);
    frame.format = ScreenFormat::BGR24;
    frame.isFullFrame = true;
    frame.timestamp = 1234567890;

    // Generate screen-like data
    frame.data = generateScreenLikeData(800, 600);
    frame.originalSize = static_cast<uint32_t>(frame.data.size());

    std::cout << "Original frame data size: " << frame.data.size() << " bytes" << std::endl;

    // Compress the frame data
    Compression::LZ4Compressor compressor;
    auto compressionResult = compressor.compress(frame.data);

    if (compressionResult.success && compressionResult.ratio < 0.9) {
        frame.data = compressionResult.data;
        frame.isCompressed = true;
        std::cout << "Frame compressed: " << frame.originalSize << " -> " << frame.data.size()
                  << " bytes (ratio: " << (compressionResult.ratio * 100) << "%)" << std::endl;
    } else {
        frame.isCompressed = false;
        frame.originalSize = 0;
        std::cout << "Frame not compressed (ratio not beneficial)" << std::endl;
    }

    // Serialize the frame
    auto serializedData = DataIntegrity::serializeScreenFrame(frame);
    std::cout << "Serialized frame size: " << serializedData.size() << " bytes" << std::endl;

    // Deserialize the frame
    ScreenFrame deserializedFrame;
    bool deserializeSuccess = DataIntegrity::deserializeScreenFrame(serializedData, deserializedFrame);

    if (!deserializeSuccess) {
        std::cout << "❌ Frame deserialization FAILED" << std::endl;
        return false;
    }

    std::cout << "✅ Frame deserialization successful" << std::endl;
    std::cout << "  MonitorId: " << deserializedFrame.monitorId << std::endl;
    std::cout << "  Region: (" << deserializedFrame.region.x << ", " << deserializedFrame.region.y
              << ", " << deserializedFrame.region.width << ", " << deserializedFrame.region.height << ")" << std::endl;
    std::cout << "  Format: " << static_cast<int>(deserializedFrame.format) << std::endl;
    std::cout << "  IsCompressed: " << (deserializedFrame.isCompressed ? "true" : "false") << std::endl;
    std::cout << "  OriginalSize: " << deserializedFrame.originalSize << std::endl;
    std::cout << "  Data size: " << deserializedFrame.data.size() << " bytes" << std::endl;

    // If compressed, test decompression
    if (deserializedFrame.isCompressed && deserializedFrame.originalSize > 0) {
        auto decompressed = compressor.decompress(deserializedFrame.data, deserializedFrame.originalSize);

        if (decompressed.size() == deserializedFrame.originalSize) {
            std::cout << "✅ Frame decompression successful: " << decompressed.size() << " bytes" << std::endl;
            return true;
        } else {
            std::cout << "❌ Frame decompression FAILED: expected " << deserializedFrame.originalSize
                      << ", got " << decompressed.size() << std::endl;
            return false;
        }
    }

    return true;
}

int main() {
    std::cout << "=== LZ4 Compression Test Suite ===" << std::endl;

    bool allTestsPassed = true;

    // Test 1: Small random data
    auto randomData = generateRandomData(1024);
    allTestsPassed &= testLZ4Compression("Small Random Data (1KB)", randomData);

    // Test 2: Repeating pattern data
    auto repeatingData = generateRepeatingData(4096);
    allTestsPassed &= testLZ4Compression("Repeating Pattern Data (4KB)", repeatingData);

    // Test 3: Large random data
    auto largeRandomData = generateRandomData(64 * 1024);
    allTestsPassed &= testLZ4Compression("Large Random Data (64KB)", largeRandomData);

    // Test 4: Screen-like data
    auto screenData = generateScreenLikeData(640, 480);
    allTestsPassed &= testLZ4Compression("Screen-like Data (640x480 BGR24)", screenData);

    // Test 5: Large screen data
    auto largeScreenData = generateScreenLikeData(1920, 1080);
    allTestsPassed &= testLZ4Compression("Large Screen Data (1920x1080 BGR24)", largeScreenData);

    // Test 6: ScreenFrame serialization with compression
    allTestsPassed &= testScreenFrameSerialization();

    std::cout << "\n=== Test Results ===" << std::endl;
    if (allTestsPassed) {
        std::cout << "✅ ALL TESTS PASSED!" << std::endl;
        return 0;
    } else {
        std::cout << "❌ SOME TESTS FAILED!" << std::endl;
        return 1;
    }
}
